{"name": "alt-run-mvp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:vite": "vite build", "build": "npm run generate-sitemap && npm run build:vite && npm run prerender-pages", "lint": "eslint .", "preview": "vite preview", "generate-sitemap": "node scripts/generate-sitemap.js", "prerender-pages": "node scripts/prerender-pages.js", "check-routes": "node scripts/check-routes.js", "check-redirects": "node scripts/check-redirects.js", "postbuild": "echo 'Build completed with pre-rendered pages and sitemap generation'"}, "dependencies": {"@react-pdf/renderer": "^4.3.0", "@supabase/supabase-js": "^2.49.4", "ics": "^3.8.1", "node-fetch": "^3.3.2", "prop-types": "^15.8.1", "react": "^18.2.0", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-router-dom": "^7.6.0", "react-share": "^5.2.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "puppeteer": "^24.9.0", "vite": "^6.3.5", "vite-plugin-html-config": "^2.0.2", "vite-plugin-static-copy": "^3.0.0"}}